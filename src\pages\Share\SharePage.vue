<script setup>
import { ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import { showToast, showDialog } from "vant";
import user from "../../Resources/share/user-test.png";

const route = useRoute();
const isValid = ref(false);
const loading = ref(true);

// 设备信息
const deviceInfo = ref({
  id: "",
  name: "",
  num: 0,
  nickName: "",
});

// 处理接受分享按钮点击
const handleAcceptShare = () => {
  showDialog({
    title: "",
    message: '即将离开微信，打开"华为智慧生活"',
    showCancelButton: true,
    cancelButtonText: "取消",
    confirmButtonText: "允许",
    className: "custom-dialog",
    beforeClose: (action) => {
      if (action === "confirm") {
        // 使用链接方式唤起应用
        openAppWithLink();
        return true;
      }
      return true;
    },
  });
};

// 使用链接方式唤起应用
const openAppWithLink = () => {
  try {
    // 创建一个隐藏的a标签
    const appLink = document.createElement("a");
    appLink.rel = "nofollow";
    appLink.href = "xiaomi://nas/open?";
    appLink.style.display = "none";
    document.body.appendChild(appLink);

    // 模拟点击链接
    appLink.click();

    // 移除链接元素
    setTimeout(() => {
      document.body.removeChild(appLink);
      showToast({
        message: "已尝试打开应用",
        position: "bottom",
      });
    }, 500);
  } catch (error) {
    console.error("打开应用失败:", error);
    showToast({
      message: "打开应用失败，请手动打开",
      position: "bottom",
    });
  }
};

// 检查分享是否过期（4小时有效期）
const checkShareExpired = (timestamp) => {
  if (!timestamp) return true;

  try {
    // 解析时间戳格式 "20250618T102756Z"
    const year = parseInt(timestamp.substring(0, 4));
    const month = parseInt(timestamp.substring(4, 6)) - 1; // 月份从0开始
    const day = parseInt(timestamp.substring(6, 8));
    const hour = parseInt(timestamp.substring(9, 11));
    const minute = parseInt(timestamp.substring(11, 13));
    const second = parseInt(timestamp.substring(13, 15));

    const shareTime = new Date(
      Date.UTC(year, month, day, hour, minute, second)
    );
    const currentTime = new Date();

    // 计算时间差（毫秒）
    const timeDiff = currentTime - shareTime;

    // 4小时 = 4 * 60 * 60 * 1000 毫秒
    const fourHours = 4 * 60 * 60 * 1000;

    return timeDiff > fourHours;
  } catch (error) {
    console.error("解析时间戳失败:", error);
    return true; // 解析失败当作过期处理
  }
};

onMounted(async () => {
  try {
    // 获取URL参数
    const { sharecode, timestamp, deviceName, nickName, deviceNum } =
      route.query;

    if (!sharecode || !timestamp) {
      showToast("分享链接参数不完整");
      isValid.value = false;
      loading.value = false;
      return;
    }

    // 检查分享是否过期
    const expired = checkShareExpired(timestamp);

    if (expired) {
      isValid.value = false;
      loading.value = false;
      return;
    }

    // 分享有效
    isValid.value = true;
    deviceInfo.value = {
      id: nickName || "未知用户",
      name: deviceName || "设备",
      num: parseInt(deviceNum) || 1,
      nickName: nickName || "未知用户",
    };

    loading.value = false;
  } catch (error) {
    console.error("验证分享链接失败:", error);
    showToast("验证分享链接失败");
    isValid.value = false;
    loading.value = false;
  }
});
</script>

<template>
  <div class="share-page">
    <van-loading v-if="loading" class="loading" vertical
      >验证分享链接中...</van-loading
    >

    <!-- 无效分享页面 -->
    <div v-if="!loading && !isValid" class="share-content invalid-share">
      <div class="logo-container">
        <van-image src="" alt="Logo" class="logo" />
      </div>
      <div class="status-text">共享失效</div>
      <div class="desc-text">该共享已过有效期</div>
      <div class="button-container">
        <van-button
          type="primary"
          class="action-button"
          @click="handleAcceptShare"
          >返回</van-button
        >
      </div>
    </div>

    <!-- 有效分享页面 -->
    <div v-if="!loading && isValid" class="share-content valid-share">
      <div class="title">小米智能存储</div>
      <div class="device-container">
        <van-image src="" alt="设备" class="device-image" />
      </div>
      <div class="avatar-container">
        <van-image round width="50" height="50" :src="user" />
      </div>
      <div class="device-info">
        {{ deviceInfo.nickName }}分享了{{ deviceInfo.num }}个{{
          deviceInfo.name
        }}给您
      </div>
      <div class="device-desc">
        接受后，您可以和{{ deviceInfo.nickName }}一起使用此设备。
      </div>
      <div class="button-container">
        <van-button
          type="primary"
          class="action-button"
          @click="handleAcceptShare"
          >接受分享</van-button
        >
      </div>
    </div>

    <!-- test -->
    <div class="test-links" style="margin-top: 20px; display: none">
      <a rel="nofollow" href="xiaomi://nas/open?">直接打开应用</a>
    </div>
  </div>
</template>

<style scoped>
.share-page {
  min-height: calc(100% - 35px);
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #ffffff;
  color: #333;
  transition: background-color 0.3s, color 0.3s;
  padding-top: 35px;
}
.title {
  font-family: Albert Sans;
  font-weight: 500;
  font-style: Medium;
  font-size: 20px;
  line-height: 100%;
  letter-spacing: 0px;
  color: #000;
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.share-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 24px;
  box-sizing: border-box;
  background-color: #ffffff;
  transition: background-color 0.3s;
}

.logo-container,
.device-container {
  margin-bottom: 53px;
  text-align: center;
}

.logo {
  width: 150px;
  height: 150px;
  border-radius: 45px;
}

.device-image {
  width: 150px;
  height: 150px;
  border-radius: 45px;
  margin-top: 79px;
}

.avatar-container {
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
}

.status-text {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 8px;
  text-align: center;
}

.desc-text,
.device-info,
.device-desc {
 font-family: Albert Sans;
font-weight: 500;
font-style: Medium;
font-size: 20px;
line-height: 100%;
letter-spacing: 0px;

}

.device-info {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  transition: color 0.3s;
}

.device-desc {
  margin-bottom: 30px;
}

.button-container {
  width: 100%;
  display: flex;
  justify-content: center;
  position: relative;
  margin-top: auto;
}

.action-button {
  width: 100%;
  /* max-width: 300px; */
  height: 44px;
  border-radius: 22px;
}

.custom-dialog .van-dialog {
  width: 80%;
  max-width: 320px;
  border-radius: 12px;
  overflow: hidden;
}

.custom-dialog .van-dialog__content {
  padding: 20px 16px;
}

.custom-dialog .van-dialog__message {
  font-size: 16px;
  font-weight: 400;
  padding: 8px 0;
}

.custom-dialog .van-dialog__footer {
  display: flex;
}

.custom-dialog .van-button {
  flex: 1;
  border: none;
  height: 44px;
  font-size: 16px;
}

.custom-dialog .van-dialog__cancel {
  color: #666;
}

.custom-dialog .van-dialog__confirm {
  color: #1989fa;
  font-weight: 500;
}

.custom-dialog .van-hairline--top::after {
  border-top-color: #ebedf0;
}

.custom-dialog .van-dialog__footer::after {
  border-color: #ebedf0;
}

@media (prefers-color-scheme: dark) {
  /* 暗色模式下的Dialog样式 */
  .custom-dialog .van-dialog {
    background-color: #1c1c1e;
  }

  .custom-dialog .van-dialog__message {
    color: #f5f5f5;
  }

  .custom-dialog .van-dialog__cancel {
    color: #aaaaaa;
  }

  .custom-dialog .van-hairline--top::after {
    border-top-color: #2c2c2e;
  }

  .custom-dialog .van-dialog__footer::after {
    border-color: #2c2c2e;
  }

  /* Vant组件在暗色模式下的样式覆盖 */
  .van-toast {
    background-color: #2c2c2e;
    color: #f5f5f5;
  }

  .van-loading__text {
    color: #f5f5f5;
  }

  .share-page {
    background-color: #121212;
    color: #f5f5f5;
  }

  .share-content {
    background-color: #121212;
  }

  .status-text {
    color: #f5f5f5;
  }

  .desc-text,
  .device-desc {
    color: #aaaaaa;
  }

  .device-info {
    color: #e0e0e0;
  }
}
</style>
